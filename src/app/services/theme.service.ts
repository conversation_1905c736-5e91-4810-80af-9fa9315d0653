import { Injectable, signal, effect } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'zerouali-tours-theme';
  
  // Signal pour gérer l'état du dark mode
  isDarkMode = signal<boolean>(false);

  constructor() {
    // Initialiser le thème au démarrage
    this.initializeTheme();
    
    // Effect pour appliquer le thème quand il change
    effect(() => {
      this.applyTheme(this.isDarkMode());
    });
  }

  private initializeTheme(): void {
    // Vérifier si on est côté client (pas SSR)
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem(this.THEME_KEY);
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      
      if (savedTheme) {
        this.isDarkMode.set(savedTheme === 'dark');
      } else {
        this.isDarkMode.set(prefersDark);
      }
    }
  }

  private applyTheme(isDark: boolean): void {
    if (typeof document !== 'undefined') {
      const htmlElement = document.documentElement;
      
      if (isDark) {
        htmlElement.classList.add('dark');
      } else {
        htmlElement.classList.remove('dark');
      }
      
      // Sauvegarder la préférence
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(this.THEME_KEY, isDark ? 'dark' : 'light');
      }
    }
  }

  toggleTheme(): void {
    this.isDarkMode.update(current => !current);
  }

  setTheme(isDark: boolean): void {
    this.isDarkMode.set(isDark);
  }
}
