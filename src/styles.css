@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles for Zerouali Tours */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans antialiased;
    @apply bg-white dark:bg-gray-900;
    @apply text-gray-900 dark:text-white;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}

@layer components {
  .btn-primary {
    @apply bg-brand-primary hover:bg-brand-primary/90 text-black font-medium px-6 py-3 rounded-lg transition-all duration-200 transform hover:scale-105;
  }

  .btn-secondary {
    @apply bg-transparent border-2 border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-black font-medium px-6 py-3 rounded-lg transition-all duration-200;
  }

  .section-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .container-max {
    @apply max-w-7xl mx-auto;
  }
}
