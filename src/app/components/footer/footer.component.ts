import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.css'
})
export class FooterComponent {
  currentYear = new Date().getFullYear();

  scrollToTop(): void {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  openSocialLink(platform: string): void {
    const links = {
      facebook: 'https://www.facebook.com/share/1A7s73oe13/?mibextid=LQQJ4d',
      instagram: 'https://www.instagram.com/zerouali.tours?igsh=emtlOWR5czB6OHow',
      tiktok: 'https://www.tiktok.com/@zerouali_tours?_t=8rusZeXaQwJ&_r=1'
    };
    
    const url = links[platform as keyof typeof links];
    if (url) {
      window.open(url, '_blank');
    }
  }
}
