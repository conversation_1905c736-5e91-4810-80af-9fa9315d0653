<nav class="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
  <div class="container-max section-padding">
    <div class="flex items-center justify-between h-16">
      <!-- Logo -->
      <div class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-brand-primary rounded-lg flex items-center justify-center">
          <span class="text-black font-bold text-lg">Z</span>
        </div>
        <span class="text-xl font-semibold text-gray-900 dark:text-white">Zerouali Tours</span>
      </div>

      <!-- Navigation Desktop -->
      <div class="hidden md:flex items-center space-x-8">
        <button 
          (click)="scrollToSection('hero')"
          class="text-gray-700 dark:text-gray-300 hover:text-brand-primary dark:hover:text-brand-primary transition-colors duration-200">
          Accueil
        </button>
        <button 
          (click)="scrollToSection('vehicles')"
          class="text-gray-700 dark:text-gray-300 hover:text-brand-primary dark:hover:text-brand-primary transition-colors duration-200">
          Véhicules
        </button>
        <button 
          (click)="scrollToSection('services')"
          class="text-gray-700 dark:text-gray-300 hover:text-brand-primary dark:hover:text-brand-primary transition-colors duration-200">
          Services
        </button>
        <button 
          (click)="scrollToSection('contact')"
          class="text-gray-700 dark:text-gray-300 hover:text-brand-primary dark:hover:text-brand-primary transition-colors duration-200">
          Contact
        </button>
      </div>

      <!-- Actions Desktop -->
      <div class="hidden md:flex items-center space-x-4">
        <!-- Toggle Dark Mode -->
        <button 
          (click)="toggleTheme()"
          class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
          [attr.aria-label]="isDarkMode() ? 'Activer le mode clair' : 'Activer le mode sombre'">
          @if (isDarkMode()) {
            <!-- Icône Soleil -->
            <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          } @else {
            <!-- Icône Lune -->
            <svg class="w-5 h-5 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
          }
        </button>

        <!-- CTA Button -->
        <button 
          (click)="scrollToSection('contact')"
          class="btn-primary">
          Réserver
        </button>
      </div>

      <!-- Menu Mobile Button -->
      <div class="md:hidden flex items-center space-x-2">
        <!-- Toggle Dark Mode Mobile -->
        <button 
          (click)="toggleTheme()"
          class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200">
          @if (isDarkMode()) {
            <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
            </svg>
          } @else {
            <svg class="w-5 h-5 text-gray-700 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
            </svg>
          }
        </button>

        <!-- Hamburger Menu -->
        <button 
          (click)="toggleMenu()"
          class="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200">
          @if (!isMenuOpen) {
            <svg class="w-6 h-6 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
          } @else {
            <svg class="w-6 h-6 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          }
        </button>
      </div>
    </div>

    <!-- Menu Mobile -->
    @if (isMenuOpen) {
      <div class="md:hidden border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 animate-fade-in">
        <div class="py-4 space-y-2">
          <button 
            (click)="scrollToSection('hero')"
            class="block w-full text-left px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-brand-primary transition-colors duration-200">
            Accueil
          </button>
          <button 
            (click)="scrollToSection('vehicles')"
            class="block w-full text-left px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-brand-primary transition-colors duration-200">
            Véhicules
          </button>
          <button 
            (click)="scrollToSection('services')"
            class="block w-full text-left px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-brand-primary transition-colors duration-200">
            Services
          </button>
          <button 
            (click)="scrollToSection('contact')"
            class="block w-full text-left px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-brand-primary transition-colors duration-200">
            Contact
          </button>
          <div class="px-4 pt-2">
            <button 
              (click)="scrollToSection('contact')"
              class="btn-primary w-full">
              Réserver
            </button>
          </div>
        </div>
      </div>
    }
  </div>
</nav>
