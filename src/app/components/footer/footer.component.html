<footer class="bg-gray-900 dark:bg-black text-white">
  <div class="container-max section-padding">
    <!-- Main footer content -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 py-16">
      <!-- Company info -->
      <div class="lg:col-span-2">
        <div class="flex items-center space-x-2 mb-6">
          <div class="w-10 h-10 bg-brand-primary rounded-lg flex items-center justify-center">
            <span class="text-black font-bold text-xl">Z</span>
          </div>
          <span class="text-2xl font-semibold">Zerouali Tours</span>
        </div>
        <p class="text-gray-300 mb-6 max-w-md leading-relaxed">
          Votre partenaire de confiance pour des locations uniques, alliant confort, innovation et respect de l'environnement.
        </p>
        <div class="flex space-x-4">
          <button 
            (click)="openSocialLink('facebook')"
            class="w-10 h-10 bg-gray-800 hover:bg-brand-primary rounded-lg flex items-center justify-center transition-colors duration-200 group">
            <svg class="w-5 h-5 text-gray-300 group-hover:text-black" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
          </button>
          <button 
            (click)="openSocialLink('instagram')"
            class="w-10 h-10 bg-gray-800 hover:bg-brand-primary rounded-lg flex items-center justify-center transition-colors duration-200 group">
            <svg class="w-5 h-5 text-gray-300 group-hover:text-black" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.718-1.297c-.875.807-2.026 1.297-3.323 1.297s-2.448-.49-3.323-1.297c-.807-.875-1.297-2.026-1.297-3.323s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323z"/>
            </svg>
          </button>
          <button 
            (click)="openSocialLink('tiktok')"
            class="w-10 h-10 bg-gray-800 hover:bg-brand-primary rounded-lg flex items-center justify-center transition-colors duration-200 group">
            <svg class="w-5 h-5 text-gray-300 group-hover:text-black" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-lg font-semibold mb-6">Liens rapides</h3>
        <ul class="space-y-3">
          <li>
            <button class="text-gray-300 hover:text-brand-primary transition-colors duration-200">
              Accueil
            </button>
          </li>
          <li>
            <button class="text-gray-300 hover:text-brand-primary transition-colors duration-200">
              Véhicules
            </button>
          </li>
          <li>
            <button class="text-gray-300 hover:text-brand-primary transition-colors duration-200">
              Services
            </button>
          </li>
          <li>
            <button class="text-gray-300 hover:text-brand-primary transition-colors duration-200">
              Contact
            </button>
          </li>
        </ul>
      </div>

      <!-- Contact Info -->
      <div>
        <h3 class="text-lg font-semibold mb-6">Contact</h3>
        <div class="space-y-3">
          <div class="flex items-start space-x-3">
            <svg class="w-5 h-5 text-brand-primary mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-gray-300 text-sm">264 Tafoukt Extension Essaouira</span>
          </div>
          <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 text-brand-primary flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
            </svg>
            <span class="text-gray-300 text-sm">+212 698-925494</span>
          </div>
          <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 text-brand-primary flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
            </svg>
            <span class="text-gray-300 text-sm">Aamir&#64;zeroualitours.com</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom footer -->
    <div class="border-t border-gray-800 py-8">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <p class="text-gray-400 text-sm mb-4 md:mb-0">
          © {{ currentYear }} Zerouali Tours. Tous droits réservés.
        </p>
        <div class="flex items-center space-x-6">
          <button class="text-gray-400 hover:text-brand-primary text-sm transition-colors duration-200">
            Politique de confidentialité
          </button>
          <button class="text-gray-400 hover:text-brand-primary text-sm transition-colors duration-200">
            Conditions d'utilisation
          </button>
          <button 
            (click)="scrollToTop()"
            class="w-10 h-10 bg-gray-800 hover:bg-brand-primary rounded-lg flex items-center justify-center transition-colors duration-200 group">
            <svg class="w-5 h-5 text-gray-300 group-hover:text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</footer>
