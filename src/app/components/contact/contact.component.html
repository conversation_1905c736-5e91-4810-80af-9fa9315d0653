<section id="contact" class="py-20 bg-white dark:bg-gray-900">
  <div class="container-max section-padding">
    <div class="text-center mb-16">
      <h2 class="text-3xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
        Contactez-nous
      </h2>
      <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
        Prêt à vivre une expérience de location unique ? Contactez-nous dès maintenant.
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
      <!-- Informations de contact -->
      <div class="space-y-8">
        <div>
          <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
            Nos coordonnées
          </h3>
          
          <!-- Adresse -->
          <div class="flex items-start space-x-4 mb-6">
            <div class="w-12 h-12 bg-brand-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-brand-primary" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900 dark:text-white mb-1">Adresse</h4>
              <p class="text-gray-600 dark:text-gray-300">264 Tafoukt Extension Essaouira</p>
              <button 
                (click)="openMaps()"
                class="text-brand-primary hover:underline text-sm mt-1">
                Voir sur la carte
              </button>
            </div>
          </div>

          <!-- Téléphone -->
          <div class="flex items-start space-x-4 mb-6">
            <div class="w-12 h-12 bg-brand-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-brand-primary" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900 dark:text-white mb-1">Téléphone</h4>
              <p class="text-gray-600 dark:text-gray-300">+212 698-925494</p>
              <button 
                (click)="openWhatsApp()"
                class="inline-flex items-center text-brand-primary hover:underline text-sm mt-1">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.109"/>
                </svg>
                WhatsApp
              </button>
            </div>
          </div>

          <!-- Email -->
          <div class="flex items-start space-x-4 mb-8">
            <div class="w-12 h-12 bg-brand-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-brand-primary" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900 dark:text-white mb-1">Email</h4>
              <p class="text-gray-600 dark:text-gray-300">Aamir&#64;zeroualitours.com</p>
              <p class="text-gray-600 dark:text-gray-300">zeroualitours&#64;gmail.com</p>
            </div>
          </div>

          <!-- Horaires -->
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-brand-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-brand-primary" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h4 class="font-semibold text-gray-900 dark:text-white mb-1">Disponibilité</h4>
              <p class="text-gray-600 dark:text-gray-300">24h/24 - 7j/7</p>
              <p class="text-brand-primary text-sm">Support client disponible</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Formulaire de contact -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-2xl p-8">
        <h3 class="text-2xl font-semibold text-gray-900 dark:text-white mb-6">
          Envoyez-nous un message
        </h3>

        @if (isSubmitted) {
          <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
            <div class="flex items-center">
              <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <p class="text-green-700 dark:text-green-300 font-medium">
                Message envoyé avec succès ! Nous vous répondrons rapidement.
              </p>
            </div>
          </div>
        }

        <form (ngSubmit)="onSubmit()" #contactFormRef="ngForm">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Nom complet *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                [(ngModel)]="contactForm.name"
                required
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200"
                placeholder="Votre nom">
            </div>
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Téléphone *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                [(ngModel)]="contactForm.phone"
                required
                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200"
                placeholder="+212 6XX-XXXXXX">
            </div>
          </div>

          <div class="mb-6">
            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email *
            </label>
            <input
              type="email"
              id="email"
              name="email"
              [(ngModel)]="contactForm.email"
              required
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200"
              placeholder="<EMAIL>">
          </div>

          <div class="mb-6">
            <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Message *
            </label>
            <textarea
              id="message"
              name="message"
              [(ngModel)]="contactForm.message"
              required
              rows="4"
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-brand-primary focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-none"
              placeholder="Décrivez vos besoins de location..."></textarea>
          </div>

          <button
            type="submit"
            [disabled]="!contactFormRef.form.valid || isSubmitting"
            class="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
            @if (isSubmitting) {
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-black inline" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Envoi en cours...
            } @else {
              Envoyer le message
            }
          </button>
        </form>
      </div>
    </div>
  </div>
</section>
